import React, { useState, useMemo, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { CommonNavBar } from '@/components/ui/CommonNavBar'
import { useTranslation } from 'react-i18next'
import { ThemeDetail } from '@/apis/types'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import useSWR from 'swr'
import { MirrorLoading } from 'wujieai-react-icon'
import MazeSingleTemplateList from '@/components/pages/homepage/MazeSingleTemplateList'
import { CreateBtn } from '@/components/pages/homepage/CreateBtn'
import { useBridge } from '@/hooks/useBridge'
import { ActionSheet } from '@/components/ui/ActionSheet'

/**
 * 模型详情页面
 * 显示当前模型包含的主题列表
 */
const ModelDetail: React.FC = () => {
  const [searchParams] = useSearchParams()
  const { t } = useTranslation()
  const { tabbarVisible } = useBridge()

  useEffect(() => {
    tabbarVisible({ visible: 0 })
  }, [])

  // 从URL参数获取模型信息
  const modelId = searchParams.get('id')

  // 当前选中的模板
  const [activeTemplate, setActiveTemplate] = useState<
    ThemeDetail | null | undefined
  >(null)

  // ActionSheet显示状态
  const [showActionSheet, setShowActionSheet] = useState(false)

  // 获取主题详情数据
  const { data: themeDetailData, isLoading } = useSWR(
    modelId ? [modelId] : null,
    ([id]) => _ajax.get(_api.theme_detail, { params: { id } })
  )

  // 过滤并处理主题列表数据，转换为ThemeDetail格式
  const detailList = useMemo(() => {
    const itemList = themeDetailData?.data?.data?.itemList || []
    // 将itemList转换为ThemeDetail格式以兼容MazeSingleTemplateList组件
    return itemList.map((item: any) => ({
      id: item.id,
      name: item.name,
      cover_image: item.image,
      cover_image_female: item.image,
      template_count: 1,
      type: 0,
      price: item.price || 0,
      video: {
        resultUrl: '',
      },
    })) as ThemeDetail[]
  }, [themeDetailData])

  // 设置默认选中第一个
  useEffect(() => {
    if (detailList.length > 0) {
      setActiveTemplate(detailList[0])
    }
  }, [detailList])

  return (
    <div className="w-full h-full flex flex-col">
      {/* 通用导航栏 */}
      <CommonNavBar title={themeDetailData?.data?.data?.name} />

      {/* 页面内容区域 */}
      <div className="flex-1 relative">
        {isLoading && (
          <div className="flex items-center justify-center w-full h-full">
            <MirrorLoading className="animate-spin maze-primary-text w-12 h-12" />
          </div>
        )}
        {!isLoading && detailList.length === 0 && (
          <div className="flex items-center justify-center w-full h-full">
            <div className="text-center text-white">
              <div className="text-xl font-bold opacity-65">
                {t('当前分类下还没有模板')}
              </div>
            </div>
          </div>
        )}

        {!isLoading && detailList.length > 0 && (
          <MazeSingleTemplateList
            selectTemplateList={detailList}
            activeTemplate={activeTemplate}
            setActiveTemplate={setActiveTemplate}
            listKey={`model-detail-${modelId}`}
            multiline={false}
          />
        )}
      </div>
      {!themeDetailData?.data?.data?.desc && (
        <div className="px-8 pt-2 flex gap-2 flex-shrink-0 leading-[14px]">
          <p className="flex-1 text-ellipsis text-nowrap overflow-hidden text-white text-[14px]">
            {themeDetailData?.data?.data?.desc}
            今天我看到了一只鸟儿，飞在了天空中，肆意的舞蹈，自由，非常自由今天我看到了一只鸟儿，飞在了天空中，肆意的舞蹈，自由，非常自由
          </p>
          <button
            className="text-[#e583ed] text-[14px] hover:text-[#d470db] transition-colors cursor-pointer"
            onClick={() => setShowActionSheet(true)}
          >
            More
          </button>
        </div>
      )}
      {/* 底部创建按钮 */}
      <div className="p-[16px]">
        <CreateBtn selectTemplateList={detailList} />
      </div>

      {/* ActionSheet 描述详情弹窗 */}
      <ActionSheet
        open={showActionSheet}
        onClose={() => setShowActionSheet(false)}
        content={
          <div className="text-white text-base leading-relaxed flex flex-col">
            <p className="whitespace-pre-wrap min-h-[18vh] p-12 text-[14px] overflow-y-scroll">
              {themeDetailData?.data.data.desc}
              今天我看到了一只鸟儿，飞在了天空中，肆意的舞蹈，自由，非常自由今天我看到了一只鸟儿，飞在了天空中，肆意的舞蹈，自由，非常自由今天我看到了一只鸟儿，飞在了天空中，肆意的舞蹈，自由，非常自由今天我看到了一只鸟儿，飞在了天空中，肆意的舞蹈，自由，非常自由
            </p>
            <div className="p-[16px]">
              <CreateBtn selectTemplateList={detailList} />
            </div>
          </div>
        }
      />
    </div>
  )
}

export default ModelDetail
