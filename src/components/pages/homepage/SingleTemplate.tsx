import { MirrorSexEnum } from '@/graphqls/types'
import classnames from 'classnames'
import { MyImage } from '@/components/ui/MyImage'
import classNames from 'classnames'
import styles from './SingleTemplate.module.css'
import { ThemeDetail } from '@/apis/types'
import { useSetAtom } from 'jotai'
import { isShowThemeDetailModalAtom } from '@/stores'

/** 单个模版 */
function SingleTemplate({
  item,
  active = false,
  onSelect,
  isMultiple = false,
  className,
  activeGender,
}: {
  item: ThemeDetail
  active: boolean
  isMultiple?: boolean
  className?: string
  activeGender?: string
  onSelect: () => void
}) {
  const setThemeDetailModalOpen = useSetAtom(isShowThemeDetailModalAtom)
  console.log('activeGender', activeGender)
  return (
    <div
      className={classnames(
        'relative border-[1px]',
        className,
        styles.template,
        isMultiple && styles.multiple,
        {
          [styles.active]: active,
        }
      )}
      onClick={onSelect}
    >
      {item.name && (
        <div className="maze-theme-title-bg w-full h-[7.75rem] text-[2.5rem] leading-[7.75rem] font-semibold px-[5rem] rounded-[10px] text-white absolute left-0 right-0 bottom-0 text-center text-ellipsis text-nowrap overflow-hidden">
          {item.name}
        </div>
      )}
      {/* {item.female_model_count || item.male_model_count ? (
        <div
          onClick={e => {
            e.stopPropagation()
            setThemeDetailModalOpen(true)
          }}
          className="absolute cursor-pointer text-[2rem] w-14 h-14 text-white leading-none left-6 top-6 rounded-full flex items-center justify-center ipad:scale-[0.8] ipad:left-4 ipad:bottom-4 maze-badge-default"
        >
          <span>
            {activeGender === MirrorSexEnum.FEMALE
              ? item.female_model_count
              : item.male_model_count}
          </span>
        </div>
      ) : (
        <div className="absolute no-data-found"></div>
      )} */}
      <MyImage
        src={item?.cover_image_female || item?.cover_image}
        tag="v800"
        className={classNames('rounded-[10px] h-[260px]')}
        imgClassName="object-cover"
      />
    </div>
  )
}

export default SingleTemplate
