import { TemplateListProps } from './const'
import SingleTemplate from './SingleTemplate'
import { useNavigate } from 'react-router-dom'
import { ThemeDetail } from '@/apis/types'
import { MirrorLoading } from 'wujieai-react-icon'
import { useEffect } from 'react'
import { preload } from 'swr'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'

/** 多行模版列表 - 简单平铺布局 */
const MultipleTemplateList = ({
  selectTemplateList,
  activeTemplate,
  setActiveTemplate,
  activeGender,
}: TemplateListProps) => {
  const navigate = useNavigate()

  // 预加载详情页数据
  useEffect(() => {
    if (selectTemplateList && selectTemplateList.length > 0) {
      const preloadDetailData = async () => {
        try {
          console.log(
            '🚀 开始预加载详情页数据，模板数量:',
            selectTemplateList.length
          )

          // 创建与详情页相同的 fetcher 函数
          const fetcher = ([id]: [number]) =>
            _ajax.get(_api.theme_detail, { params: { id } })

          // 限制并发数量，避免一次性发起太多请求
          const batchSize = 3
          let successCount = 0
          let failCount = 0

          for (let i = 0; i < selectTemplateList.length; i += batchSize) {
            const batch = selectTemplateList.slice(i, i + batchSize)

            const results = await Promise.allSettled(
              batch.map(template => {
                try {
                  // 使用 SWR 的 preload 函数预加载数据
                  preload([template.id], fetcher)
                  return Promise.resolve(template.id)
                } catch (error) {
                  return Promise.reject(error)
                }
              })
            )

            // 统计成功和失败的数量
            results.forEach((result, index) => {
              if (result.status === 'fulfilled') {
                successCount++
                console.log(`✅ 模板 ${batch[index].id} 预加载成功`)
              } else {
                failCount++
                console.warn(
                  `❌ 模板 ${batch[index].id} 预加载失败:`,
                  result.reason
                )
              }
            })

            // 批次间添加小延迟，避免请求过于密集
            if (i + batchSize < selectTemplateList.length) {
              await new Promise(resolve => setTimeout(resolve, 50))
            }
          }

          console.log(
            `🎉 详情页数据预加载完成！成功: ${successCount}, 失败: ${failCount}`
          )
        } catch (error) {
          console.warn('⚠️ 详情页数据预加载过程中发生错误:', error)
        }
      }

      // 延迟执行预加载，避免阻塞主要渲染
      const timer = setTimeout(preloadDetailData, 200)
      return () => clearTimeout(timer)
    }
  }, [selectTemplateList])

  // 处理模型详情页面跳转
  const handleModelDetailClick = (template: ThemeDetail) => {
    console.log('点击模板:', template)
    // 先设置选中的模板，这会触发 PictureFramework 中的 useEffect 来更新 selectedThemeDetailAtom
    setActiveTemplate(template)
    // 使用 setTimeout 确保状态更新完成后再导航
    setTimeout(() => {
      navigate(`/models?id=${template.id}`)
    }, 0)
  }

  return (
    <>
      {selectTemplateList && selectTemplateList.length > 0 ? (
        <div className="grid grid-cols-2 gap-8 py-2 pb-[32px]">
          {selectTemplateList.map(template => (
            <div key={template.id} className="w-full">
              <SingleTemplate
                isMultiple
                activeGender={activeGender}
                item={template}
                active={template.id === activeTemplate?.id}
                onSelect={() => handleModelDetailClick(template)}
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 my-[34px] p-4 text-center">
          <MirrorLoading className="animate-spin maze-primary-text w-12 h-12" />
        </div>
      )}
    </>
  )
}
export default MultipleTemplateList
