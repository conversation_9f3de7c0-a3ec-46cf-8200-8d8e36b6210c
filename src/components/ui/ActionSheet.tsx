import React, { useEffect, useState } from 'react'
import { cn } from '@/utils/shad'
import { SvgIcon } from './SvgIcon'

interface ActionSheetProps {
  /** 是否显示ActionSheet */
  open: boolean
  /** 标题 */
  title?: string
  /** 内容 */
  content?: React.ReactNode
  /** 关闭回调 */
  onClose?: () => void
  /** 自定义样式类名 */
  className?: string
  /** 内容区域自定义样式类名 */
  contentClassName?: string
}

/**
 * 类似iOS ActionSheet的底部弹出组件
 * 支持从底部滑入动画和遮罩层
 */
export const ActionSheet: React.FC<ActionSheetProps> = ({
  open,
  title,
  content,
  onClose,
  className,
  contentClassName,
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const [startY, setStartY] = useState(0)
  const [currentY, setCurrentY] = useState(0)
  const [isDragging, setIsDragging] = useState(false)

  useEffect(() => {
    if (open) {
      setIsVisible(true)
      // 延迟一帧开始动画，确保DOM已渲染
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          setIsAnimating(true)
        })
      })
    } else {
      setIsAnimating(false)
      // 等待动画结束后隐藏组件
      const timer = setTimeout(() => {
        setIsVisible(false)
      }, 350) // 稍微延长等待时间以匹配新的动画时长
      return () => clearTimeout(timer)
    }
  }, [open])

  // 点击遮罩层关闭
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose?.()
    }
  }

  // 阻止内容区域点击事件冒泡
  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation()
  }

  // 触摸开始
  const handleTouchStart = (e: React.TouchEvent) => {
    setStartY(e.touches[0].clientY)
    setCurrentY(e.touches[0].clientY)
    setIsDragging(true)
  }

  // 触摸移动
  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return
    setCurrentY(e.touches[0].clientY)
  }

  // 触摸结束
  const handleTouchEnd = () => {
    if (!isDragging) return
    setIsDragging(false)

    const deltaY = currentY - startY
    // 如果向下滑动超过100px，则关闭ActionSheet
    if (deltaY > 100) {
      onClose?.()
    }

    setStartY(0)
    setCurrentY(0)
  }

  if (!isVisible) return null

  return (
    <div
      className={cn(
        'fixed inset-0 z-50 flex items-end justify-center',
        'transition-all duration-[350ms] ease-out',
        isAnimating ? 'bg-black/60 backdrop-blur-sm' : 'bg-black/0'
      )}
      onClick={handleOverlayClick}
    >
      <div
        className={cn(
          'w-full max-w-lg mx-4 mb-4 rounded-t-3xl overflow-hidden',
          'phone:mx-0 phone:mb-0 phone:rounded-t-[40px] phone:max-w-none',
          'transition-all duration-[350ms] ease-[cubic-bezier(0.34,1.56,0.64,1)]',
          'bg-[var(--primary-bg)] border border-neutral-300 shadow-box-neutral',
          isAnimating
            ? 'translate-y-0 scale-100 opacity-100'
            : 'translate-y-full scale-100 opacity-90',
          isDragging && currentY > startY
            ? `translate-y-[${Math.min(currentY - startY, 200)}px]`
            : '',
          className
        )}
        onClick={handleContentClick}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* 顶部拖拽指示器 */}
        <div className="flex justify-center pt-4 pb-3">
          <div className="w-12 h-1 bg-neutral-400 rounded-full opacity-60" />
        </div>

        {/* 标题栏 */}
        {title && (
          <div className="flex items-center justify-between px-6 py-4">
            <h3 className="text-lg font-medium text-[var(--primary-text)]">
              {title}
            </h3>
            <button
              onClick={onClose}
              className="p-2 rounded-full hover:bg-neutral-300 transition-colors"
            >
              <SvgIcon
                src="/images/icons/close.svg"
                alt="关闭"
                className="w-5 h-5 opacity-70"
              />
            </button>
          </div>
        )}

        {/* 内容区域 */}
        <div
          className={cn(
            'px-6 py-8 max-h-[60vh] overflow-y-auto scrollbar-hidden',
            contentClassName
          )}
        >
          {content}
        </div>
      </div>
    </div>
  )
}
