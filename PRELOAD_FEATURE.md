# 详情页接口预加载功能

## 功能概述

在图片列表页面加载完成后，自动预加载所有模板的详情页接口数据，以便用户点击进入详情页时能够快速显示内容，减少等待时间。

## 实现原理

1. **监听列表数据变化**: 使用 `useEffect` 监听 `selectTemplateList` 的变化
2. **批量预加载**: 当列表数据加载完成后，批量预加载每个模板的详情数据
3. **SWR 预加载**: 使用 SWR 的 `preload` 函数将数据预加载到缓存中
4. **缓存复用**: 详情页使用相同的缓存 key 和 fetcher，直接从缓存中获取数据

## 技术实现

### 修改的文件
- `src/components/pages/homepage/MultipleTemplateList.tsx`

### 核心代码
```typescript
// 预加载详情页数据
useEffect(() => {
  if (selectTemplateList && selectTemplateList.length > 0) {
    const preloadDetailData = async () => {
      try {
        console.log('🚀 开始预加载详情页数据，模板数量:', selectTemplateList.length)

        // 创建与详情页相同的 fetcher 函数
        const fetcher = ([id]: [number]) =>
          _ajax.get(_api.theme_detail, { params: { id } })

        // 限制并发数量，避免一次性发起太多请求
        const batchSize = 3
        let successCount = 0
        let failCount = 0

        for (let i = 0; i < selectTemplateList.length; i += batchSize) {
          const batch = selectTemplateList.slice(i, i + batchSize)

          const results = await Promise.allSettled(
            batch.map(template => {
              try {
                // 使用 SWR 的 preload 函数预加载数据
                preload([template.id], fetcher)
                return Promise.resolve(template.id)
              } catch (error) {
                return Promise.reject(error)
              }
            })
          )

          // 统计成功和失败的数量
          results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
              successCount++
              console.log(`✅ 模板 ${batch[index].id} 预加载成功`)
            } else {
              failCount++
              console.warn(`❌ 模板 ${batch[index].id} 预加载失败:`, result.reason)
            }
          })

          // 批次间添加小延迟，避免请求过于密集
          if (i + batchSize < selectTemplateList.length) {
            await new Promise(resolve => setTimeout(resolve, 50))
          }
        }

        console.log(`🎉 详情页数据预加载完成！成功: ${successCount}, 失败: ${failCount}`)
      } catch (error) {
        console.warn('⚠️ 详情页数据预加载过程中发生错误:', error)
      }
    }

    // 延迟执行预加载，避免阻塞主要渲染
    const timer = setTimeout(preloadDetailData, 200)
    return () => clearTimeout(timer)
  }
}, [selectTemplateList])
```

## 优化特性

1. **SWR 官方 API**: 使用 SWR 的 `preload` 函数，符合官方最佳实践
2. **批量处理**: 每次处理 3 个模板，避免并发请求过多
3. **错误处理**: 使用 `Promise.allSettled` 确保单个请求失败不影响其他请求
4. **延迟执行**: 延迟 200ms 执行，避免阻塞主要渲染
5. **批次间延迟**: 批次间添加 50ms 延迟，避免请求过于密集
6. **详细日志**: 提供详细的预加载状态日志，便于调试
7. **缓存复用**: 与详情页使用相同的缓存 key 和 fetcher，确保数据复用

## 使用效果

- ✅ 列表页加载完成后自动开始预加载
- ✅ 用户点击详情页时数据已缓存，快速显示
- ✅ 预加载失败不影响正常功能
- ✅ 控制台显示详细的预加载状态

## 注意事项

1. 预加载功能不会影响页面的正常渲染和交互
2. 预加载失败时会在控制台显示警告，但不会影响用户体验
3. 预加载的数据会被 SWR 缓存管理，遵循 SWR 的缓存策略
4. 如果需要禁用预加载功能，可以注释掉相关的 `useEffect` 代码

## 测试方法

1. 打开浏览器开发者工具的控制台
2. 访问列表页面
3. 观察控制台中的预加载日志
4. 点击任意模板进入详情页，观察加载速度
